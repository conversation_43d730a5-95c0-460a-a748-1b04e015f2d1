package service

import model.User
import model.AuthState

/**
 * Interface para o serviço de autenticação
 * Segue o padrão IHandleAuthService
 */
interface IHandleAuthService {
    suspend fun login(username: String, password: String): AuthState
    fun logout(): AuthState
    fun getCurrentUser(): User?
}

/**
 * Implementação mockada do serviço de autenticação
 * Responsabilidade única: gerenciar autenticação
 * Extensível para implementações reais (API, banco de dados, etc.)
 */
class MockAuthService : IHandleAuthService {
    
    private var currentUser: User? = null
    
    // Usuários mockados para teste
    private val mockUsers = mapOf(
        "kevin" to "123"
    )
    
    /**
     * Simula login com delay para demonstrar loading
     * @param username nome do usuário
     * @param password senha do usuário
     * @return AuthState com resultado da autenticação
     */
    override suspend fun login(username: String, password: String): AuthState {
        return try {
            // Simula delay de rede
            kotlinx.coroutines.delay(1000)
            
            val storedPassword = mockUsers[username]
            
            if (storedPassword != null && storedPassword == password) {
                currentUser = User(username = username, isAuthenticated = true)
                AuthState.Authenticated(currentUser!!)
            } else {
                AuthState.Error("Usuário ou senha inválidos")
            }
        } catch (e: Exception) {
            AuthState.Error("Erro interno: ${e.message}")
        }
    }
    
    /**
     * Realiza logout do usuário atual
     * @return AuthState.NotAuthenticated
     */
    override fun logout(): AuthState {
        currentUser = null
        return AuthState.NotAuthenticated
    }
    
    /**
     * Retorna o usuário atualmente autenticado
     * @return User? usuário atual ou null se não autenticado
     */
    override fun getCurrentUser(): User? {
        return currentUser
    }
}
