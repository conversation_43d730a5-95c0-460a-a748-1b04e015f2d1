package service

import config.ApiConfig
import io.ktor.client.call.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import model.*
import utils.Logger

/**
 * Implementação real do serviço de autenticação usando a API
 * Responsabilidade única: gerenciar autenticação via HTTP
 * Extensível para diferentes tipos de autenticação (OAuth, JWT, etc.)
 */
class ApiAuthService(
    private val httpClient: IHandleHttpClient = HttpClientProvider.getInstance()
) : IHandleAuthService {
    
    private var currentUser: User? = null
    private var authToken: String? = null
    
    /**
     * Realiza login via API
     * @param username nome do usuário ou email
     * @param password senha do usuário
     * @return AuthState com resultado da autenticação
     */
    override suspend fun login(username: String, password: String): AuthState {
        return try {
            val loginRequest = LoginRequest(
                usernameOrEmail = username,
                password = password
            )
            
            Logger.httpRequest("POST", "${ApiConfig.baseUrl}${ApiConfig.Endpoints.LOGIN}", loginRequest)
            
            val response: HttpResponse = httpClient.client.post("${ApiConfig.baseUrl}${ApiConfig.Endpoints.LOGIN}") {
                contentType(ContentType.Application.Json)
                setBody(loginRequest)
            }

            
            val responseBody = response.bodyAsText()
            Logger.log("Resposta da API: $responseBody")
            Logger.httpResponse(
                status = response.status.toString(),
                body = responseBody,
                headers = response.headers.entries().associate { it.key to it.value }
            )
            
            when (response.status) {
                HttpStatusCode.OK -> {
                    val loginResponse: LoginResponse = response.body()
                    Logger.success("Login realizado com sucesso!") 
                    Logger.debug("LoginResponse parseado: $loginResponse")
                    
                    if (loginResponse.token != null && loginResponse.user != null) {
                        authToken = loginResponse.token
                        currentUser = loginResponse.user.toDomainUser()
                        Logger.success("Usuário autenticado: ${currentUser?.username}")
                        AuthState.Authenticated(currentUser!!)
                    } else {
                        Logger.error("Resposta inválida do servidor - token ou usuário ausente")
                        AuthState.Error("Resposta inválida do servidor")
                    }
                }
                
                HttpStatusCode.Unauthorized -> {
                    Logger.warning("Credenciais inválidas")
                    AuthState.Error("Usuário ou senha inválidos")
                }
                
                HttpStatusCode.BadRequest -> {
                    try {
                        val errorResponse: ApiError = response.body()
                        Logger.warning("Erro 400 - ${errorResponse.message}")
                        AuthState.Error(errorResponse.message ?: "Dados inválidos")
                    } catch (e: Exception) {
                        Logger.error("Erro ao parsear resposta 400: ${e.message}")
                        AuthState.Error("Dados inválidos")
                    }
                }
                
                else -> {
                    Logger.error("Erro do servidor: ${response.status}")
                    AuthState.Error("Erro do servidor: ${response.status.description}")
                }
            }
            
        } catch (e: Exception) {
            Logger.error("Erro de conexão: ${e.message}")
            Logger.debug("Stack trace: ${e.stackTraceToString()}")
            AuthState.Error("Erro de conexão: ${e.message}")
        }
    }
    
    /**
     * Realiza logout do usuário atual
     * @return AuthState.NotAuthenticated
     */
    override fun logout(): AuthState {
        Logger.info("Fazendo logout do usuário: ${currentUser?.username ?: "N/A"}")
        currentUser = null
        authToken = null
        Logger.success("Logout realizado com sucesso")
        return AuthState.NotAuthenticated
    }
    
    /**
     * Retorna o usuário atualmente autenticado
     * @return User? usuário atual ou null se não autenticado
     */
    override fun getCurrentUser(): User? {
        Logger.debug("Consultando usuário atual: ${currentUser?.username ?: "Nenhum usuário autenticado"}")
        return currentUser
    }
    
    /**
     * Retorna o token de autenticação atual
     * @return String? token atual ou null se não autenticado
     */
    fun getAuthToken(): String? {
        return authToken
    }
    
    /**
     * Verifica se o usuário está autenticado
     * @return Boolean true se autenticado
     */
    fun isAuthenticated(): Boolean {
        return currentUser != null && authToken != null
    }
}
