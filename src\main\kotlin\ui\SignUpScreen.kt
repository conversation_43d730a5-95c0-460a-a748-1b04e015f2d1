package ui

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Visibility
import androidx.compose.material.icons.filled.VisibilityOff
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import ui.theme.AppColors

/**
 * Interface para propriedades da tela de registro
 * Segue o padrão IHandleSignUpScreenProps
 */
interface IHandleSignUpScreenProps {
    val onNavigateBack: () -> Unit
    val onRegister: (username: String, email: String, password: String, fullName: String) -> Unit
    val isLoading: Boolean
    val errorMessage: String?
}

/**
 * Implementação das propriedades da tela de registro
 */
data class SignUpScreenProps(
    override val onNavigateBack: () -> Unit = {},
    override val onRegister: (username: String, email: String, password: String, fullName: String) -> Unit = { _, _, _, _ -> },
    override val isLoading: Boolean = false,
    override val errorMessage: String? = null
) : IHandleSignUpScreenProps

/**
 * Tela de Registro
 * Responsabilidade única: interface de cadastro de usuário
 * Lógica separada do JSX através de props
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SignUpScreen(props: IHandleSignUpScreenProps) {
    
    // Estados locais da UI
    var username by remember { mutableStateOf("") }
    var email by remember { mutableStateOf("") }
    var fullName by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    var confirmPassword by remember { mutableStateOf("") }
    var passwordVisible by remember { mutableStateOf(false) }
    var confirmPasswordVisible by remember { mutableStateOf(false) }
    
    // Validações
    val isFormValid = username.isNotBlank() && 
                     email.isNotBlank() && 
                     password.isNotBlank() && 
                     confirmPassword.isNotBlank() &&
                     password == confirmPassword &&
                     email.contains("@") &&
                     password.length >= 6
    
    // Layout da tela com gradiente de fundo
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                Brush.verticalGradient(
                    colors = listOf(
                        AppColors.Background,
                        AppColors.Surface
                    )
                )
            )
    ) {
        // Botão de voltar
        IconButton(
            onClick = props.onNavigateBack,
            modifier = Modifier
                .padding(16.dp)
                .align(Alignment.TopStart)
        ) {
            Icon(
                imageVector = Icons.Default.ArrowBack,
                contentDescription = "Voltar",
                tint = AppColors.OnBackground
            )
        }
        
        // Conteúdo principal
        Card(
            modifier = Modifier
                .width(420.dp)
                .padding(24.dp)
                .align(Alignment.Center),
            elevation = CardDefaults.cardElevation(defaultElevation = 12.dp),
            colors = CardDefaults.cardColors(
                containerColor = AppColors.Surface
            ),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier.padding(32.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(20.dp)
            ) {
                
                // Título e subtítulo
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = "Criar Conta",
                        style = MaterialTheme.typography.headlineLarge,
                        fontWeight = FontWeight.Bold,
                        color = AppColors.OnSurface
                    )
                    Text(
                        text = "Preencha os dados para se cadastrar",
                        style = MaterialTheme.typography.bodyLarge,
                        color = AppColors.OnSurfaceVariant
                    )
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // Campo de nome completo
                OutlinedTextField(
                    value = fullName,
                    onValueChange = { fullName = it },
                    label = { Text("Nome Completo") },
                    modifier = Modifier.fillMaxWidth(),
                    enabled = !props.isLoading,
                    singleLine = true,

                    shape = RoundedCornerShape(12.dp)
                )
                
                // Campo de usuário
                OutlinedTextField(
                    value = username,
                    onValueChange = { username = it },
                    label = { Text("Usuário") },
                    modifier = Modifier.fillMaxWidth(),
                    enabled = !props.isLoading,
                    singleLine = true,

                    shape = RoundedCornerShape(12.dp)
                )
                
                // Campo de email
                OutlinedTextField(
                    value = email,
                    onValueChange = { email = it },
                    label = { Text("Email") },
                    modifier = Modifier.fillMaxWidth(),
                    enabled = !props.isLoading,
                    singleLine = true,
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Email),

                    shape = RoundedCornerShape(12.dp)
                )
                
                // Campo de senha
                OutlinedTextField(
                    value = password,
                    onValueChange = { password = it },
                    label = { Text("Senha (mín. 6 caracteres)") },
                    modifier = Modifier.fillMaxWidth(),
                    enabled = !props.isLoading,
                    visualTransformation = if (passwordVisible) VisualTransformation.None else PasswordVisualTransformation(),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password),
                    singleLine = true,

                    shape = RoundedCornerShape(12.dp),
                    trailingIcon = {
                        IconButton(onClick = { passwordVisible = !passwordVisible }) {
                            Icon(
                                imageVector = if (passwordVisible) Icons.Default.VisibilityOff else Icons.Default.Visibility,
                                contentDescription = if (passwordVisible) "Ocultar senha" else "Mostrar senha",
                                tint = AppColors.OnSurfaceVariant
                            )
                        }
                    }
                )
                
                // Campo de confirmar senha
                OutlinedTextField(
                    value = confirmPassword,
                    onValueChange = { confirmPassword = it },
                    label = { Text("Confirmar Senha") },
                    modifier = Modifier.fillMaxWidth(),
                    enabled = !props.isLoading,
                    visualTransformation = if (confirmPasswordVisible) VisualTransformation.None else PasswordVisualTransformation(),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password),
                    singleLine = true,

                    shape = RoundedCornerShape(12.dp),
                    trailingIcon = {
                        IconButton(onClick = { confirmPasswordVisible = !confirmPasswordVisible }) {
                            Icon(
                                imageVector = if (confirmPasswordVisible) Icons.Default.VisibilityOff else Icons.Default.Visibility,
                                contentDescription = if (confirmPasswordVisible) "Ocultar senha" else "Mostrar senha",
                                tint = AppColors.OnSurfaceVariant
                            )
                        }
                    },
                    isError = confirmPassword.isNotBlank() && password != confirmPassword
                )
                
                // Validação de senha
                if (confirmPassword.isNotBlank() && password != confirmPassword) {
                    Text(
                        text = "As senhas não coincidem",
                        color = AppColors.Error,
                        style = MaterialTheme.typography.bodySmall
                    )
                }
                
                // Mensagem de erro
                props.errorMessage?.let { error ->
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = AppColors.ErrorContainer
                        ),
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        Text(
                            text = error,
                            color = AppColors.OnErrorContainer,
                            style = MaterialTheme.typography.bodyMedium,
                            modifier = Modifier.padding(12.dp),
                            textAlign = TextAlign.Center
                        )
                    }
                }
                
                // Botão de cadastro
                Button(
                    onClick = { 
                        props.onRegister(username, email, password, fullName)
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(56.dp),
                    enabled = !props.isLoading && isFormValid,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = AppColors.Primary,
                        contentColor = AppColors.OnPrimary,
                        disabledContainerColor = AppColors.OutlineVariant,
                        disabledContentColor = AppColors.OnSurfaceVariant
                    ),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    if (props.isLoading) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(20.dp),
                            color = AppColors.OnPrimary,
                            strokeWidth = 2.dp
                        )
                    } else {
                        Text(
                            text = "Criar Conta",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.SemiBold
                        )
                    }
                }
                
                // Link para voltar ao login
                Row(
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Já tem uma conta? ",
                        style = MaterialTheme.typography.bodyMedium,
                        color = AppColors.OnSurfaceVariant
                    )
                    Text(
                        text = "Faça login",
                        style = MaterialTheme.typography.bodyMedium,
                        color = AppColors.Primary,
                        fontWeight = FontWeight.SemiBold,
                        modifier = Modifier.clickable { props.onNavigateBack() }
                    )
                }
            }
        }
    }
}
