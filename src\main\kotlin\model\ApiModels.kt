package model

import kotlinx.serialization.Serializable

/**
 * Modelos de dados para comunicação com a API
 * Responsabilidade única: representar estruturas de dados da API
 */

/**
 * Interface para requisição de login
 * Segue o padrão IHandleLoginRequest
 */
interface IHandleLoginRequest {
    val usernameOrEmail: String
    val password: String
}

/**
 * Requisição de login
 * Baseado na documentação da API: {"usernameOrEmail": "joao_silva", "password": "MinhaSenh@123"}
 */
@Serializable
data class LoginRequest(
    override val usernameOrEmail: String,
    override val password: String
) : IHandleLoginRequest

/**
 * Interface para resposta de login
 * Segue o padrão IHandleLoginResponse
 */
interface IHandleLoginResponse {
    val token: String?
    val user: ApiUser?
    val message: String?
}

/**
 * Resposta de login da API
 */
@Serializable
data class LoginResponse(
    override val token: String? = null,
    override val user: ApiUser? = null,
    override val message: String? = null
) : IHandleLoginResponse

/**
 * Interface para usuário da API
 * Segue o padrão IHandleApiUser
 */
interface IHandleApiUser {
    val id: String?
    val username: String
    val email: String?
    val createdAt: String?
}

/**
 * Modelo de usuário retornado pela API
 */
@Serializable
data class ApiUser(
    override val id: String? = null,
    override val username: String,
    override val email: String? = null,
    override val createdAt: String? = null
) : IHandleApiUser

/**
 * Interface para resposta de erro da API
 * Segue o padrão IHandleApiError
 */
interface IHandleApiError {
    val error: String
    val message: String?
    val statusCode: Int?
}

/**
 * Resposta de erro da API
 */
@Serializable
data class ApiError(
    override val error: String,
    override val message: String? = null,
    override val statusCode: Int? = null
) : IHandleApiError

/**
 * Extensão para converter ApiUser em User do domínio
 */
fun ApiUser.toDomainUser(): User {
    return User(
        username = this.username,
        isAuthenticated = true
    )
}
