package ui

import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.*
import hooks.useAuth
import model.AuthState
import service.AuthServiceFactory

/**
 * Componente principal da aplicação
 * Responsabilidade única: gerenciar navegação entre telas baseada no estado de autenticação
 * Extensível para adicionar novas telas e rotas
 */
@Composable
fun App() {
    
    // Serviço de autenticação (injeção de dependência)
    // Usa factory para escolher entre mock e API real
    val authService = remember { AuthServiceFactory.createDefaultAuthService() }
    
    // Hook de autenticação (lógica separada do JSX)
    val authHook = useAuth(authService)
    
    // Tema da aplicação
    MaterialTheme {
        
        // Navegação baseada no estado de autenticação
        when (val state = authHook.authState) {
            is AuthState.Loading,
            is AuthState.NotAuthenticated,
            is AuthState.Error -> {
                // Exibe tela de login
                LoginScreen(
                    props = LoginScreenProps(authHook = authHook)
                )
            }
            
            is AuthState.Authenticated -> {
                // Exibe tela home
                HomeScreen(
                    props = HomeScreenProps(
                        user = state.user,
                        onLogout = { authHook.logout() }
                    )
                )
            }
        }
    }
}
