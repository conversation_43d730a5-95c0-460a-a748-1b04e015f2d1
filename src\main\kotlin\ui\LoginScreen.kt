package ui

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.unit.dp
import hooks.IHandleAuthHook

/**
 * Interface para props da tela de login
 * Segue o padrão IHandleLoginScreenProps
 */
interface IHandleLoginScreenProps {
    val authHook: IHandleAuthHook
}

/**
 * Implementação das props da tela de login
 */
data class LoginScreenProps(
    override val authHook: IHandleAuthHook
) : IHandleLoginScreenProps

/**
 * Tela de Login
 * Responsabilidade única: interface de autenticação
 * Lógica separada do JSX através do hook
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LoginScreen(props: IHandleLoginScreenProps) {
    
    // Estados locais da UI (separados da lógica de negócio)
    var username by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    
    // Lógica de negócio delegada ao hook
    val authHook = props.authHook
    
    // Layout da tela
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Card(
            modifier = Modifier
                .width(400.dp)
                .padding(16.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                
                // Título
                Text(
                    text = "Login",
                    style = MaterialTheme.typography.headlineMedium
                )
                
                // Campo de usuário
                OutlinedTextField(
                    value = username,
                    onValueChange = { username = it },
                    label = { Text("Usuário") },
                    modifier = Modifier.fillMaxWidth(),
                    enabled = !authHook.isLoading,
                    singleLine = true
                )
                
                // Campo de senha
                OutlinedTextField(
                    value = password,
                    onValueChange = { password = it },
                    label = { Text("Senha") },
                    modifier = Modifier.fillMaxWidth(),
                    enabled = !authHook.isLoading,
                    visualTransformation = PasswordVisualTransformation(),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password),
                    singleLine = true
                )
                
                // Mensagem de erro
                authHook.errorMessage?.let { error ->
                    Text(
                        text = error,
                        color = MaterialTheme.colorScheme.error,
                        style = MaterialTheme.typography.bodySmall
                    )
                }
                
                // Botão de login
                Button(
                    onClick = { 
                        authHook.clearError()
                        authHook.login(username, password) 
                    },
                    modifier = Modifier.fillMaxWidth(),
                    enabled = !authHook.isLoading && username.isNotBlank() && password.isNotBlank()
                ) {
                    if (authHook.isLoading) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            color = MaterialTheme.colorScheme.onPrimary
                        )
                    } else {
                        Text("Entrar")
                    }
                }
                
                // Dica para teste
                Text(
                    text = "Dica: usuário 'kevin', senha '123'",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}
