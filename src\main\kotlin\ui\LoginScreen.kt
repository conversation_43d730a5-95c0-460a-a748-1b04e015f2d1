package ui

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Visibility
import androidx.compose.material.icons.filled.VisibilityOff
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import hooks.IHandleAuthHook
import ui.theme.AppColors

/**
 * Interface para props da tela de login
 * Segue o padrão IHandleLoginScreenProps
 */
interface IHandleLoginScreenProps {
    val authHook: IHandleAuthHook
    val onNavigateToRegister: () -> Unit
    val onNavigateToForgotPassword: () -> Unit
}

/**
 * Implementação das props da tela de login
 */
data class LoginScreenProps(
    override val authHook: IHandleAuthHook,
    override val onNavigateToRegister: () -> Unit = {},
    override val onNavigateToForgotPassword: () -> Unit = {}
) : IHandleLoginScreenProps

/**
 * Tela de Login
 * Responsabilidade única: interface de autenticação
 * Lógica separada do JSX através do hook
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LoginScreen(props: IHandleLoginScreenProps) {

    // Estados locais da UI (separados da lógica de negócio)
    var username by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    var passwordVisible by remember { mutableStateOf(false) }

    // Lógica de negócio delegada ao hook
    val authHook = props.authHook

    // Layout da tela com gradiente de fundo
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                Brush.verticalGradient(
                    colors = listOf(
                        AppColors.Background,
                        AppColors.Surface
                    )
                )
            ),
        contentAlignment = Alignment.Center
    ) {
        Card(
            modifier = Modifier
                .width(420.dp)
                .padding(24.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 12.dp),
            colors = CardDefaults.cardColors(
                containerColor = AppColors.Surface
            ),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier.padding(32.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(20.dp)
            ) {

                // Título e subtítulo
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = "Bem-vindo",
                        style = MaterialTheme.typography.headlineLarge,
                        fontWeight = FontWeight.Bold,
                        color = AppColors.OnSurface
                    )
                    Text(
                        text = "Faça login para continuar",
                        style = MaterialTheme.typography.bodyLarge,
                        color = AppColors.OnSurfaceVariant
                    )
                }

                Spacer(modifier = Modifier.height(8.dp))

                // Campo de usuário
                OutlinedTextField(
                    value = username,
                    onValueChange = { username = it },
                    label = { Text("Usuário") },
                    modifier = Modifier.fillMaxWidth(),
                    enabled = !authHook.isLoading,
                    singleLine = true,

                    shape = RoundedCornerShape(12.dp)
                )

                // Campo de senha
                OutlinedTextField(
                    value = password,
                    onValueChange = { password = it },
                    label = { Text("Senha") },
                    modifier = Modifier.fillMaxWidth(),
                    enabled = !authHook.isLoading,
                    visualTransformation = if (passwordVisible) VisualTransformation.None else PasswordVisualTransformation(),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password),
                    singleLine = true,

                    shape = RoundedCornerShape(12.dp),
                    trailingIcon = {
                        IconButton(onClick = { passwordVisible = !passwordVisible }) {
                            Icon(
                                imageVector = if (passwordVisible) Icons.Default.VisibilityOff else Icons.Default.Visibility,
                                contentDescription = if (passwordVisible) "Ocultar senha" else "Mostrar senha",
                                tint = AppColors.OnSurfaceVariant
                            )
                        }
                    }
                )

                // Mensagem de erro
                authHook.errorMessage?.let { error ->
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = AppColors.ErrorContainer
                        ),
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        Text(
                            text = error,
                            color = AppColors.OnErrorContainer,
                            style = MaterialTheme.typography.bodyMedium,
                            modifier = Modifier.padding(12.dp),
                            textAlign = TextAlign.Center
                        )
                    }
                }

                // Botão de login
                Button(
                    onClick = {
                        authHook.clearError()
                        authHook.login(username, password)
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(56.dp),
                    enabled = !authHook.isLoading && username.isNotBlank() && password.isNotBlank(),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = AppColors.Primary,
                        contentColor = AppColors.OnPrimary,
                        disabledContainerColor = AppColors.OutlineVariant,
                        disabledContentColor = AppColors.OnSurfaceVariant
                    ),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    if (authHook.isLoading) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(20.dp),
                            color = AppColors.OnPrimary,
                            strokeWidth = 2.dp
                        )
                    } else {
                        Text(
                            text = "Entrar",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.SemiBold
                        )
                    }
                }

                // Links de navegação
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = "Esqueceu sua senha?",
                        style = MaterialTheme.typography.bodyMedium,
                        color = AppColors.Primary,
                        modifier = Modifier.clickable { props.onNavigateToForgotPassword() }
                    )

                    Row(
                        horizontalArrangement = Arrangement.Center,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Não tem uma conta? ",
                            style = MaterialTheme.typography.bodyMedium,
                            color = AppColors.OnSurfaceVariant
                        )
                        Text(
                            text = "Cadastre-se",
                            style = MaterialTheme.typography.bodyMedium,
                            color = AppColors.Primary,
                            fontWeight = FontWeight.SemiBold,
                            modifier = Modifier.clickable { props.onNavigateToRegister() }
                        )
                    }
                }
            }
        }
    }
}
