package ui.theme

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color

/**
 * Cores personalizadas para o tema escuro
 * Responsabilidade única: definir paleta de cores
 */
object AppColors {
    // Cores primárias - tons de azul
    val Primary = Color(0xFF3B82F6)
    val PrimaryVariant = Color(0xFF1E40AF)
    val OnPrimary = Color(0xFFFFFFFF)
    
    // Cores secundárias - tons de cinza azulado
    val Secondary = Color(0xFF64748B)
    val SecondaryVariant = Color(0xFF475569)
    val OnSecondary = Color(0xFFFFFFFF)
    
    // Cores de fundo - tons escuros
    val Background = Color(0xFF0F172A)
    val Surface = Color(0xFF1E293B)
    val SurfaceVariant = Color(0xFF334155)
    val OnBackground = Color(0xFFE2E8F0)
    val OnSurface = Color(0xFFE2E8F0)
    val OnSurfaceVariant = Color(0xFF94A3B8)
    
    // Cores de erro
    val Error = Color(0xFFEF4444)
    val OnError = Color(0xFFFFFFFF)
    val ErrorContainer = Color(0xFF7F1D1D)
    val OnErrorContainer = Color(0xFFFECACA)
    
    // Cores de sucesso
    val Success = Color(0xFF10B981)
    val OnSuccess = Color(0xFFFFFFFF)
    val SuccessContainer = Color(0xFF064E3B)
    val OnSuccessContainer = Color(0xFFA7F3D0)
    
    // Cores de warning
    val Warning = Color(0xFFF59E0B)
    val OnWarning = Color(0xFF000000)
    val WarningContainer = Color(0xFF78350F)
    val OnWarningContainer = Color(0xFFFED7AA)
    
    // Outline e divisores
    val Outline = Color(0xFF475569)
    val OutlineVariant = Color(0xFF334155)
}

/**
 * Esquema de cores escuro personalizado
 */
private val DarkColorScheme = darkColorScheme(
    primary = AppColors.Primary,
    onPrimary = AppColors.OnPrimary,
    primaryContainer = AppColors.PrimaryVariant,
    onPrimaryContainer = AppColors.OnPrimary,
    
    secondary = AppColors.Secondary,
    onSecondary = AppColors.OnSecondary,
    secondaryContainer = AppColors.SecondaryVariant,
    onSecondaryContainer = AppColors.OnSecondary,
    
    background = AppColors.Background,
    onBackground = AppColors.OnBackground,
    
    surface = AppColors.Surface,
    onSurface = AppColors.OnSurface,
    surfaceVariant = AppColors.SurfaceVariant,
    onSurfaceVariant = AppColors.OnSurfaceVariant,
    
    error = AppColors.Error,
    onError = AppColors.OnError,
    errorContainer = AppColors.ErrorContainer,
    onErrorContainer = AppColors.OnErrorContainer,
    
    outline = AppColors.Outline,
    outlineVariant = AppColors.OutlineVariant
)

/**
 * Esquema de cores claro (fallback)
 */
private val LightColorScheme = lightColorScheme(
    primary = Color(0xFF1976D2),
    onPrimary = Color(0xFFFFFFFF),
    background = Color(0xFFFFFBFE),
    surface = Color(0xFFFFFBFE),
    onBackground = Color(0xFF1C1B1F),
    onSurface = Color(0xFF1C1B1F),
)

/**
 * Tema principal da aplicação
 * Responsabilidade única: aplicar tema consistente
 * Extensível para diferentes variações de tema
 */
@Composable
fun AppTheme(
    darkTheme: Boolean = true, // Sempre usar tema escuro por padrão
    content: @Composable () -> Unit
) {
    val colorScheme = if (darkTheme) {
        DarkColorScheme
    } else {
        LightColorScheme
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = Typography(),
        content = content
    )
}
